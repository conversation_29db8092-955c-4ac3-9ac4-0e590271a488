# 🚀 Page Extractor: The World’s Best Single-Page AI Scraper 🦾🌍

> **“Extract the impossible. Scrape the invisible. Unleash the BEAST!”**

Page Extractor is the ultimate, AI-powered web scraper that outsmarts every other tool on the market. It not only grabs static and dynamic content, but also uses AI to interact with web pages—clicking, scrolling, and revealing hidden gems. Whether you need a quick CLI scrape or a robust server API, Page Extractor is your new secret weapon.  

---

## ✨ Features

- 🤖 **AI-Powered Extraction**: Detects and interacts with dynamic elements (tabs, accordions, modals, lazy-loaded content, etc.)
- 📄 **Markdown & HTML Output**: Clean, readable markdown and HTML files—auto-saved with timestamps.
- 📊 **Performance Monitoring**: Tracks CPU, memory, and phase-by-phase progress.
- 🔥 **Two Ways to Use**: CLI for quick jobs, or Server API for full integration.

---

## 🚦 Quick Start

### 1. Installation

```bash
git clone https://github.com/yourname/page-extracter-lamda.git
cd page-extracter-lamda

# Install dependencies (Node.js >= 20 required)
pnpm install
# or
npm install
```

> **Note:** Copy `.env.example` to `.env` and add your Google AI API key for full AI features:
>
> ```bash
> cp .env.example .env
> # Edit .env and add GOOGLE_GEMINI_KEY
> ```

---

## 🖥️ CLI Mode

### Basic Usage

```bash
pnpm run cli <url> [options]
# or
npm run cli -- <url> [options]
```

#### Options

- `-q, --query <text>`: Focus extraction on a specific topic (e.g., "Find pricing info")
- `-m, --mode <mode>`: `normal` or `beast` (default: normal)
- `-h, --help`: Show help

#### Examples

```bash
pnpm run cli https://example.com
pnpm run cli https://example.com -q "Find pricing information"
pnpm run cli https://example.com -m beast
```

- Extracted content is saved as:
  - 📝 `*.md` (Markdown)
  - 🌐 `*.html` (Cleaned HTML)

---

## 🌐 Server Mode (API)

### Start the Server

```bash
pnpm run server
# or
npm run start
```

### Endpoints

| Method | Endpoint         | Description                        |
|--------|------------------|------------------------------------|
| GET    | `/`              | Health check                       |
| GET    | `/health`        | Detailed health check              |
| POST   | `/scrape`        | Stream scraping (SSE)              |
| POST   | `/scrape-sync`   | Synchronous scraping               |

---

### Example: Scrape via API

#### POST `/scrape`

- **Request Body:**
  ```json
  {
    "url": "https://example.com",
    "query": "Find pricing info", // optional
    "mode": "beast" // or "normal", optional
  }
  ```

- **cURL Example:**
  ```bash
  curl -X POST http://localhost:3000/scrape \
    -H "Content-Type: application/json" \
    -d '{"url": "https://example.com", "mode": "normal"}'
  ```

#### POST `/scrape-sync`

- Same payload as above, but gets the full result in one go.

---

## 🛠️ Configuration

- Edit `.env` for advanced options (timeouts, streaming, output dir, etc).
- Requires a Google AI API key for advanced AI extraction.

---

## 🏆 Why Page Extractor?

- 🚀 **Beats every other scraper**: Handles the hardest pages, thanks to intellegent content managment with AI.
- 🦄 **Super easy to use**: One command or one API call.
- 🌈 **Future-proof**: Designed for the next era of web automation.

---

## 🧑‍💻 Contributing

PRs and stars welcome! Let’s make scraping magical together. ✨

---

## 📃 License

MIT

---

This README will make your repo pop and help users get started instantly.  
If you want to tweak the project name, endpoint URLs, or add more badges, just let me know!
