{"name": "page-extractor-playwright", "version": "1.0.0", "description": "A comprehensive web scraper built with <PERSON><PERSON> that extracts content from web pages, including dynamic elements revealed through AI-powered interaction detection - supports local and remote browsers", "main": "src/server.js", "type": "module", "engines": {"node": ">=20.0.0"}, "scripts": {"start": "node src/server.js", "cli": "node src/cli.js", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix", "format": "prettier --write src/**/*.js", "postinstall": "npx -y playwright-core install --with-deps --only-shell chromium"}, "author": "", "license": "MIT", "dependencies": {"@ai-sdk/google": "^1.2.22", "@hono/node-server": "^1.17.0", "ai": "^4.3.19", "dotenv": "^17.2.0", "hono": "^4.8.5", "playwright-core": "^1.54.1", "rehype-format": "^5.0.1", "rehype-highlight": "^7.0.2", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.1", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-stringify": "^11.0.0", "unified": "^11.0.5", "zod": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.31.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/sdk-node": "^0.203.0", "@types/node": "^24.0.15", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "globals": "^16.3.0", "langfuse-vercel": "^3.38.4", "prettier": "^3.6.2"}}