import dotenv from 'dotenv';

import {
  closeBrowser,
  getBrowser,
  getPage,
  initBrowser,
} from './browser-ops/BrowserManager.js';
import { navigateToUrl } from './browser-ops/PageNavigator.js';
import infiniteScrollUntilNoMoreNewNetworkRequest from './browser-ops/PageScroller.js';
import { MAX_RETRY_COUNT, RETRY_DELAY } from './config.js';
import {
  convertAndImproveMarkdownFromHTML,
  convertAndImproveMarkdownFromMarkdown,
} from './core/AIMarkdownHandler.js';
import { combineContent } from './core/ContentCombiner.js';
import {
  convertToMarkdown,
  fixAndFormatHTML,
} from './core/ContentConvertor.js';
import { handleIframes } from './core/IframesHandler.js';
import {
  findInteractiveElements,
  processInteractiveElementsContent,
} from './core/InteractiveElementProcessor.js';
import {
  ERROR_CATEGORIES,
  getRetryConfig,
  handleError,
} from './utils/GlobalErrorHandler.js';
import { createSimpleLogger } from './utils/SimpleLogger.js';
import { SimplePerformanceMonitor } from './utils/SimplePerformanceMonitor.js';

dotenv.config();

export const SCRAPING_MODES = {
  NORMAL: 'normal',
  BEAST: 'beast',
};

/**
 * Create scraping context with shared state and utilities
 * @returns {Object} Scraping context with shared utilities
 */
function createScrapingContext() {
  return {
    currentMode: SCRAPING_MODES.NORMAL,
    browser: null,
    page: null,
    performanceMonitor: new SimplePerformanceMonitor(),
  };
}

/**
 * Process scraping in normal mode (simple extraction)
 * @param {Object} context - Scraping context
 * @param {string} userQuery - User query for content extraction
 */
async function processNormalMode(context, userQuery) {
  context.performanceMonitor.startPhase('content-extraction');

  // after processing, remove junk from html like script tags, style tags, etc.
  await context.page.evaluate(() => {
    document
      .querySelectorAll("style, link, script[src^='https://']")
      .forEach(element => element.remove());
  });

  const rawHTML = await context.page.content();
  await closeBrowser();

  const cleanedHTML = await fixAndFormatHTML(rawHTML);
  const rawMarkdown = await convertToMarkdown(cleanedHTML);

  let finalMarkdown = rawMarkdown;
  if (userQuery) {
    const improvedMarkdown = await convertAndImproveMarkdownFromMarkdown(
      rawMarkdown,
      userQuery
    );
    finalMarkdown = improvedMarkdown;
  }
  context.performanceMonitor.endPhase();
  return { enhancedError: null, markdown: finalMarkdown, html: cleanedHTML };
}

/**
 * Process scraping in beast mode (advanced extraction with interactive elements)
 * @param {Object} context - Scraping context
 * @param {string} userQuery - Optional user query for focused content extraction
 * @returns {Object} Processing result with enhancedError if any
 */
async function processBeastMode(context, userQuery) {
  let enhancedError = null;
  let finalMarkdown = null;
  let cleanedHTML = null;

  context.performanceMonitor.startPhase('AI element detection');

  const clonedHTML = await context.page.evaluate(() => {
    const clonedPage = document.cloneNode(true);
    clonedPage
      .querySelectorAll('script, link')
      .forEach(element => element.remove());

    clonedPage.querySelectorAll('*').forEach(element => {
      if (
        element.tagName === 'IFRAME' ||
        element.tagName === 'CODE' ||
        element.tagName === 'PRE'
      ) {
        return;
      }

      if (element.tagName !== 'TABLE' && !element.closest('table')) {
        Array.from(element.attributes).forEach(attr => {
          if (
            attr.name !== 'class' &&
            attr.name !== 'id' &&
            !attr.name.startsWith('data-') &&
            !attr.name.startsWith('aria-')
          ) {
            element.removeAttribute(attr.name);
          }
        });
      }
    });
    return clonedPage.documentElement.outerHTML;
  });

  if (clonedHTML.length > 500000) {
    // Skip AI analysis for very large HTML content
    const result = await processNormalMode(context, userQuery);
    enhancedError = result.enhancedError;
    finalMarkdown = result.markdown;
    cleanedHTML = result.html;
    context.performanceMonitor.endPhase();
  } else {
    let interactiveElements = { elements: [] };
    try {
      interactiveElements = await findInteractiveElements(
        clonedHTML,
        userQuery
      );
    } catch (error) {
      const handledError = await handleError(error, {
        operation: 'findInteractiveElements',
        url: context.page?.url?.() || 'unknown',
        userQuery,
      });

      if (!handledError.shouldRetry) {
        enhancedError = handledError;
      } else {
        throw handledError; // Allow retry for retryable errors
      }
    }
    context.performanceMonitor.endPhase();
    context.performanceMonitor.startPhase('interactive processing');
    let dynamicContents = [];
    if (interactiveElements.elements.length > 0) {
      try {
        dynamicContents = await processInteractiveElementsContent(
          context.page,
          interactiveElements
        );
      } catch (error) {
        const handledError = await handleError(error, {
          operation: 'processInteractiveElementsContent',
          url: context.page?.url?.() || 'unknown',
          elementsCount: interactiveElements.elements.length,
          userQuery,
        });

        if (!handledError.shouldRetry) {
          enhancedError = handledError;
        } else {
          throw handledError; // Allow retry for retryable errors
        }
      }
    }
    context.performanceMonitor.endPhase();
    context.performanceMonitor.startPhase('content processing');

    const combinedHtml = await combineContent(context.page, dynamicContents);
    await closeBrowser();
    cleanedHTML = await fixAndFormatHTML(combinedHtml);

    // Step 6: Convert to markdown and improve with AI
    if (userQuery) {
      const improvedMarkdown = await convertAndImproveMarkdownFromHTML(
        cleanedHTML,
        userQuery,
        null // No streaming service in regular mode
      );
      finalMarkdown = improvedMarkdown;
    } else {
      const rawMarkdown = await convertToMarkdown(cleanedHTML);
      finalMarkdown = rawMarkdown;
    }
  }

  context.performanceMonitor.endPhase();
  return { enhancedError, markdown: finalMarkdown, html: cleanedHTML };
}

/**
 * Main scraper function that orchestrates the entire process
 * @param {string} url - The URL to scrape
 * @param {string} userQuery - Optional user query for focused content extraction
 * @param {string} mode - Scraping mode (NORMAL or BEAST, default: BEAST)
 * @returns {Promise<boolean>} - Success status
 */
export async function scrape(
  url,
  userQuery = '',
  mode = SCRAPING_MODES.NORMAL
) {
  let retryCount = 0;
  let success = false;
  let enhancedError = null;
  let context = null;
  let result = null; // Store the actual content result

  while (retryCount < MAX_RETRY_COUNT && !success) {
    try {
      // Create scraping context
      context = createScrapingContext();
      context.currentMode = mode;

      // Start performance monitoring
      context.performanceMonitor.start();

      context.performanceMonitor.startPhase('browser setup');
      const browserInitialized = await initBrowser();
      if (!browserInitialized) {
        throw new Error('Failed to initialize browser');
      }

      context.browser = getBrowser();
      context.page = getPage();

      // Set browser context for enhanced monitoring
      context.performanceMonitor.setBrowserContext(context.page);

      context.performanceMonitor.endPhase();

      context.performanceMonitor.startPhase('page loading');
      await navigateToUrl(context.page, url);
      await infiniteScrollUntilNoMoreNewNetworkRequest(context.page);
      context.performanceMonitor.endPhase();

      context.performanceMonitor.startPhase('iframe processing');
      await handleIframes(context.page);
      context.performanceMonitor.endPhase();

      if (context.currentMode === SCRAPING_MODES.NORMAL) {
        result = await processNormalMode(context, userQuery);
        enhancedError = result.enhancedError;
      } else {
        result = await processBeastMode(context, userQuery);
        enhancedError = result.enhancedError;
      }

      success = true;

      // Stop performance monitoring
      context.performanceMonitor.stop();

      // Final status is handled by SimpleLogger
    } catch (error) {
      // Handle the error with our global error handler
      const handledError = await handleError(error, {
        operation: 'scrape',
        url,
        attempt: retryCount + 1,
        mode: context?.currentMode || mode,
      });

      // Check if this error should stop retries
      if (
        !handledError.shouldRetry ||
        handledError.category === ERROR_CATEGORIES.AUTH ||
        handledError.category === ERROR_CATEGORIES.RATE_LIMIT
      ) {
        // Scraping stopped - non-retryable error
        break; // Don't retry for auth, rate limit, or non-retryable errors
      }

      retryCount++;

      if (retryCount < MAX_RETRY_COUNT) {
        const retryConfig = getRetryConfig(handledError);
        const delay =
          retryConfig.strategy === 'exponential'
            ? RETRY_DELAY * Math.pow(2, retryCount - 1)
            : RETRY_DELAY;

        // Retrying after delay
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        // Maximum retry attempts reached
      }
    } finally {
      // Always close browser in finally block
      if (context && context.browser) {
        await closeBrowser();
      }

      // Stop performance monitoring if it was started
      if (
        context &&
        context.performanceMonitor &&
        context.performanceMonitor.monitoringInterval
      ) {
        context.performanceMonitor.stop();
      }
    }
  }

  // Return both success status and content data
  return {
    success,
    data: result
      ? {
          markdown: result.markdown,
          enhancedError: result.enhancedError,
        }
      : null,
  };
}

/**
 * Streaming-enabled scraper function with support for local browser
 * @param {string} url - The URL to scrape
 * @param {string} userQuery - Optional user query for focused content extraction
 * @param {string} mode - Scraping mode (NORMAL or BEAST, default: BEAST)
 * @param {Function} progressCallback - Optional callback for streaming progress updates
 * @returns {Promise<Object>} - Structured result with markdown and HTML content
 */
export async function scrapeWithStreaming(
  url,
  userQuery = '',
  mode = SCRAPING_MODES.BEAST,
  progressCallback = null
) {
  const startTime = Date.now();
  let retryCount = 0;
  let success = false;
  let enhancedError = null;
  let context = null;
  let finalMarkdown = null;
  // let finalHtml = null;

  // Initialize simple logger
  const simpleLogger = createSimpleLogger(progressCallback);

  try {
    simpleLogger.log(`Starting scraping process for ${url}`, 'info');

    while (retryCount < MAX_RETRY_COUNT && !success) {
      try {
        simpleLogger.log(
          `Scraping ${url} (attempt ${retryCount + 1}/${MAX_RETRY_COUNT})`,
          'info'
        );

        // Create scraping context
        context = createScrapingContext();
        context.currentMode = mode;
        context.logger = simpleLogger;

        // Start performance monitoring
        context.performanceMonitor.start();

        // Step 1: Initialize browser and get page
        simpleLogger.setAction('Initializing browser and setting up page');
        context.performanceMonitor.startPhase('browser setup');

        const browserInitialized = await initBrowser();
        if (!browserInitialized) {
          throw new Error('Failed to initialize browser');
        }

        context.browser = getBrowser();
        context.page = getPage();

        context.performanceMonitor.setBrowserContext(context.page);
        context.performanceMonitor.endPhase();

        // Step 2: Navigate to URL and wait for page to stabilize
        simpleLogger.setAction(
          `Navigating to ${url} and waiting for page to load`
        );
        context.performanceMonitor.startPhase('page loading');

        await navigateToUrl(context.page, url);
        simpleLogger.updateAction(
          'Page loaded, performing infinite scroll to load dynamic content'
        );

        await infiniteScrollUntilNoMoreNewNetworkRequest(context.page);
        context.performanceMonitor.endPhase();

        // Step 3: Process iframes by extracting their content
        simpleLogger.setAction('Processing iframe content');
        context.performanceMonitor.startPhase('iframe processing');

        await handleIframes(context.page);
        context.performanceMonitor.endPhase();

        // Step 4: Execute scraping based on mode
        let scrapingResult;
        if (context.currentMode === SCRAPING_MODES.NORMAL) {
          scrapingResult = await processNormalModeWithStreaming(
            context,
            userQuery
          );
        } else {
          scrapingResult = await processBeastModeWithStreaming(
            context,
            userQuery
          );
        }

        enhancedError = scrapingResult.enhancedError;
        finalMarkdown = scrapingResult.markdown;
        // finalHtml = scrapingResult.html;

        success = true;

        // Stop performance monitoring
        context.performanceMonitor.stop();

        // Log final status
        if (enhancedError) {
          simpleLogger.log('Scraping completed with limitations', 'warn');
          simpleLogger.log(
            `Enhanced features: ${enhancedError.userMessage}`,
            'warn'
          );
        }
      } catch (error) {
        // Log the error
        simpleLogger.error(error, `Scraping error (attempt ${retryCount + 1})`);

        // Handle the error with our global error handler
        const handledError = await handleError(error, {
          operation: 'scrapeWithStreaming',
          url,
          attempt: retryCount + 1,
          mode: context?.currentMode || mode,
        });

        // Check if this error should stop retries
        if (
          !handledError.shouldRetry ||
          handledError.category === ERROR_CATEGORIES.AUTH ||
          handledError.category === ERROR_CATEGORIES.RATE_LIMIT
        ) {
          simpleLogger.log(
            `Scraping stopped - Error ID: ${handledError.stackId}`,
            'error'
          );
          break;
        }

        retryCount++;

        if (retryCount < MAX_RETRY_COUNT) {
          const retryConfig = getRetryConfig(handledError);
          const delay =
            retryConfig.strategy === 'exponential'
              ? RETRY_DELAY * Math.pow(2, retryCount - 1)
              : RETRY_DELAY;

          simpleLogger.log(
            `Retrying in ${delay}ms... (attempt ${retryCount + 1}/${MAX_RETRY_COUNT})`,
            'info'
          );
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          simpleLogger.log(
            `Maximum retry attempts (${MAX_RETRY_COUNT}) reached - Error ID: ${handledError.stackId}`,
            'error'
          );
        }
      } finally {
        // Always close browser in finally block
        if (context && context.browser) {
          await closeBrowser();
        }

        // Stop performance monitoring if it was started
        if (
          context &&
          context.performanceMonitor &&
          context.performanceMonitor.monitoringInterval
        ) {
          context.performanceMonitor.stop();
        }
      }
    }

    // Complete the process
    const processingTime = Date.now() - startTime;
    const result = {
      success,
      markdown: finalMarkdown,
      processingTime,
      enhancedError,
    };

    return result;
  } catch (error) {
    simpleLogger.error(error, 'Fatal error in scraping process');

    return {
      success: false,
      markdown: null,
      processingTime: Date.now() - startTime,
      error: error.message,
      enhancedError: null,
    };
  } finally {
    // Cleanup logger
    simpleLogger.destroy();
  }
}

/**
 * Process scraping in normal mode with simple logging
 * @param {Object} context - Scraping context
 * @param {string} userQuery - User query
 */
async function processNormalModeWithStreaming(context, userQuery) {
  const simpleLogger = context.logger;

  simpleLogger.setAction('Extracting and processing content in normal mode');
  context.performanceMonitor.startPhase('content-extraction');

  // Clean up the page content
  await context.page.evaluate(() => {
    document
      .querySelectorAll("style, link, script[src^='https://']")
      .forEach(element => element.remove());
  });

  simpleLogger.updateAction('Cleaning up page content and extracting HTML');
  const rawHTML = await context.page.content();
  await closeBrowser();

  simpleLogger.updateAction('Converting HTML to markdown');
  const cleanedHTML = await fixAndFormatHTML(rawHTML);
  const rawMarkdown = await convertToMarkdown(cleanedHTML);

  let finalMarkdown = rawMarkdown;

  if (userQuery) {
    simpleLogger.updateAction('Improving markdown with AI based on user query');
    finalMarkdown = await convertAndImproveMarkdownFromMarkdown(
      rawMarkdown,
      userQuery,
      simpleLogger
    );
  }

  context.performanceMonitor.endPhase();

  return {
    enhancedError: null,
    markdown: finalMarkdown,
  };
}

/**
 * Process scraping in beast mode with simple logging
 * @param {Object} context - Scraping context
 * @param {string} userQuery - User query
 */
async function processBeastModeWithStreaming(context, userQuery) {
  const simpleLogger = context.logger;
  let enhancedError = null;
  let dynamicContents = { contents: [] };

  // Step 1: Find interactive elements that might reveal hidden content
  simpleLogger.setAction('Using AI to detect interactive elements');
  context.performanceMonitor.startPhase('AI element detection');

  // Get raw HTML before cleaning
  const rawHTML = await context.page.content();

  let clonedHTML;
  try {
    clonedHTML = await context.page.evaluate(() => {
      try {
        // Starting document cloning

        // Use document.documentElement instead of document.cloneNode(true)
        // because document.cloneNode(true) doesn't include the HTML element itself
        const sourceElement = document.documentElement || document.body;
        if (!sourceElement) {
          // No documentElement or body found
          return null;
        }

        // Cloning documentElement
        const clonedPage = sourceElement.cloneNode(true);

        if (!clonedPage) {
          // Cloning returned null
          return null;
        }

        // Remove script and link elements
        const scriptsAndLinks = clonedPage.querySelectorAll('script, link');
        scriptsAndLinks.forEach(element => {
          try {
            element.remove();
          } catch (removeError) {
            // Ignore removal errors
          }
        });

        // Process element attributes
        const allElements = clonedPage.querySelectorAll('*');

        allElements.forEach((element, index) => {
          try {
            if (
              element.tagName === 'IFRAME' ||
              element.tagName === 'CODE' ||
              element.tagName === 'PRE'
            ) {
              return;
            }

            if (element.tagName !== 'TABLE' && !element.closest('table')) {
              const attrs = Array.from(element.attributes);
              attrs.forEach(attr => {
                try {
                  if (
                    attr.name !== 'class' &&
                    attr.name !== 'id' &&
                    !attr.name.startsWith('data-') &&
                    !attr.name.startsWith('aria-')
                  ) {
                    element.removeAttribute(attr.name);
                  }
                } catch (attrError) {
                  console.warn(
                    `🌐 Browser: Failed to remove attribute ${attr.name}:`,
                    attrError
                  );
                }
              });
            }
          } catch (elementError) {
            console.warn(
              `🌐 Browser: Failed to process element ${index}:`,
              elementError
            );
          }
        });

        // Generate the final HTML
        let result;
        if (clonedPage.outerHTML) {
          result = clonedPage.outerHTML;
        } else if (clonedPage.innerHTML) {
          // Fallback: wrap in HTML tags
          result = `<html>${clonedPage.innerHTML}</html>`;
        } else {
          // No outerHTML or innerHTML available
          return null;
        }

        // HTML generation complete
        return result;
      } catch (browserError) {
        // Error in cloning process - try emergency fallback
        try {
          const bodyContent = document.body ? document.body.innerHTML : '';
          return bodyContent
            ? `<html><body>${bodyContent}</body></html>`
            : null;
        } catch (fallbackError) {
          return null;
        }
      }
    });

    // HTML cloning completed

    // Additional server-side validation and fallback
    if (
      !clonedHTML ||
      typeof clonedHTML !== 'string' ||
      clonedHTML.trim().length === 0
    ) {
      // HTML cloning failed, attempting server-side fallback
      try {
        const pageContent = await context.page.content();
        if (pageContent && pageContent.length > 0) {
          clonedHTML = pageContent;
        } else {
          clonedHTML = '';
        }
      } catch (fallbackError) {
        clonedHTML = '';
      }
    }
  } catch (evaluateError) {
    // HTML cloning failed, attempting final fallback
    try {
      clonedHTML = await context.page.content();
    } catch (finalError) {
      clonedHTML = '';
    }
  }

  simpleLogger.updateAction(
    'Analyzing page structure with AI to find interactive elements'
  );

  let interactiveElements;
  if (
    !clonedHTML ||
    typeof clonedHTML !== 'string' ||
    clonedHTML.trim().length === 0
  ) {
    // Invalid or empty HTML - skip interactive element detection
    interactiveElements = { elements: [] };
  } else {
    interactiveElements = await findInteractiveElements(
      clonedHTML,
      userQuery,
      simpleLogger
    );
  }

  context.performanceMonitor.endPhase();

  // Step 2: Process interactive elements to reveal dynamic content
  simpleLogger.setAction(
    'Processing interactive elements to reveal hidden content'
  );
  context.performanceMonitor.startPhase('interactive elements processing');

  if (interactiveElements.elements.length > 0) {
    try {
      simpleLogger.updateAction(
        `Processing ${interactiveElements.elements.length} interactive elements`
      );

      dynamicContents = await processInteractiveElementsContent(
        context.page,
        interactiveElements
      );

      simpleLogger.updateAction(
        `Extracted ${dynamicContents.contents?.length || 0} pieces of dynamic content`
      );
    } catch (error) {
      const handledError = await handleError(error, {
        operation: 'processInteractiveElementsContent',
        url: context.page?.url?.() || 'unknown',
        elementsCount: interactiveElements.elements.length,
        userQuery,
      });

      if (!handledError.shouldRetry) {
        enhancedError = handledError;
      } else {
        throw handledError;
      }
    }
  }

  context.performanceMonitor.endPhase();

  // Step 3: Combine main content with dynamic content
  simpleLogger.setAction('Combining main content with dynamic content');
  context.performanceMonitor.startPhase('content processing');

  simpleLogger.updateAction(
    'Combining main content with extracted dynamic content'
  );
  const combinedHtml = await combineContent(context.page, dynamicContents);
  await closeBrowser();

  simpleLogger.updateAction('Cleaning and formatting HTML content');
  const cleanedHTML = await fixAndFormatHTML(combinedHtml);

  // Step 4: Convert to markdown and improve with AI
  let finalMarkdown;
  if (userQuery) {
    simpleLogger.updateAction(
      'Converting to markdown and improving with AI based on user query'
    );
    finalMarkdown = await convertAndImproveMarkdownFromHTML(
      cleanedHTML,
      userQuery,
      simpleLogger
    );
  } else {
    simpleLogger.updateAction('Converting HTML to markdown');
    finalMarkdown = await convertToMarkdown(cleanedHTML);
  }

  context.performanceMonitor.endPhase();

  return {
    enhancedError,
    markdown: finalMarkdown,
  };
}
