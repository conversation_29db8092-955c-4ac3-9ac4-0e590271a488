import { PAGE_OPTIONS } from '../config.js';
import { handleError } from '../utils/GlobalErrorHandler.js';
import {
  closeModals,
  findModals,
  isScrollingBlocked,
} from '../core/ModalHandler.js';

/**
 * Navigate to URL and wait for page to stabilize
 * @param {Object} page - Playwright page object
 * @param {string} url - The URL to scrape
 * @returns {Promise<boolean>} - Success status
 */
export async function navigateToUrl(page, url) {
  try {
    console.log(`🌐 Navigating to: ${url}`);
    const BLOCKED_RESOURCE_TYPES = ['media', 'font']; // 'media' covers audio and video

    await page.route('**/*', route => {
      if (BLOCKED_RESOURCE_TYPES.includes(route.request().resourceType())) {
        route.abort();
      } else {
        route.continue();
      }
    });

    await page.goto(url, PAGE_OPTIONS);
    await page.waitForLoadState('networkidle', { timeout: 10000 });
    await page.waitForLoadState('domcontentloaded');

    console.log('✅ Page loaded successfully');

    const scrollingBlocked = await isScrollingBlocked(page);
    console.log(`📜 Scrolling blocked: ${scrollingBlocked}`);
    if (scrollingBlocked) {
      console.log('🔄 Handling overlay modals...');
      await page.keyboard.press('Escape', { delay: 10 });
      await page.click('html', { delay: 100, force: true });
      await page.waitForTimeout(500);
    } else {
      await page.waitForTimeout(500);
    }

    const modals = await findModals(page);
    if (modals.length === 0) {
      console.log('✨ No modals found, continuing...');
    } else {
      console.log(`🎭 Found ${modals.length} modal(s), attempting to close...`);
    }

    const modalsClosed = await closeModals(page, modals);

    if (modalsClosed && modals.length > 0) {
      console.log('✅ Modals closed successfully');
    } else if (modals.length > 0) {
      console.log('⚠️ Some modals could not be closed');
    }

    return true;
  } catch (error) {
    await handleError(error, {
      operation: 'navigateToUrl',
      url,
      pageOptions: PAGE_OPTIONS,
    });
    return false;
  }
}
