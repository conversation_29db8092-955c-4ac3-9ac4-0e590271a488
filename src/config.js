import dotenv from "dotenv";
import { getKey } from "./utils/KeyManager.js";
dotenv.config();

// Runtime environment detection
export const RUNTIME_ENV = process.env.RUNTIME_ENV || "local"; // "local" or "cloudflare"

// General configuration
export const OUTPUT_DIR = process.env.OUTPUT_DIR || null;
export const MAX_RETRY_COUNT = parseInt(process.env.MAX_RETRY_COUNT, 10) || 2;
export const RETRY_DELAY = parseInt(process.env.RETRY_DELAY, 10) || 1000;

// Playwright specific options for local environment
export const BROWSER_OPTIONS = {
  headless: true,
  ignoreHTTPSErrors: true,
  args: [
    // Memory and performance optimizations
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--no-sandbox",
    "--disable-setuid-sandbox",
    "--memory-pressure-off",
    "--start-maximized",
    
    // Performance optimizations
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding",
    "--disable-features=TranslateUI",
    "--disable-ipc-flooding-protection",
    
    // Visual rendering optimizations (since you're scraping, not viewing)
    "--disable-extensions",
    "--disable-plugins",
    
    // Network optimizations
    "--aggressive-cache-discard",
    "--disable-background-networking",
    "--disable-default-apps",
    "--disable-sync",
    
    // Additional performance flags
    "--no-first-run",
    "--no-zygote",
    "--disable-web-security", // Only for scraping, not for testing user-facing applications
  ],
};

// Page navigation options
export const PAGE_OPTIONS = {
  waitUntil: "networkidle", // Updated to use the valid option for newer Playwright versions
  timeout: parseInt(process.env.PAGE_TIMEOUT, 10) || 10000,
};

// Streaming configuration
export const STREAMING_CONFIG = {
  enabled: process.env.STREAMING_ENABLED !== 'false',
  bufferSize: parseInt(process.env.STREAM_BUFFER_SIZE, 10) || 1, // Set to 1 for immediate streaming
  heartbeatInterval: parseInt(process.env.STREAM_HEARTBEAT, 10) || 5000, // 5 seconds
  maxStreamDuration: parseInt(process.env.MAX_STREAM_DURATION, 10) || 300000, // 5 minutes
};


export const LLM_MODEL_CONFIG = {
  apiKey: getKey() || process.env.GOOGLE_GEMINI_KEY || "", // Add API key to .env file
  modelName: "gemini-2.5-flash",
  smallModel: "gemini-2.5-flash",
};

