#!/usr/bin/env node

import { scrape, SCRAPING_MODES } from './WebScraper.js';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * Create a safe filename from a URL
 * @param {string} url - The URL to convert
 * @returns {string} Safe filename
 */
function createSafeFilename(url) {
  try {
    const urlObj = new URL(url);
    let filename = urlObj.hostname + urlObj.pathname;

    filename = filename.replace(/[<>:"/\\|?*]/g, '_');
    filename = filename.replace(/_+/g, '_');
    filename = filename.replace(/^[_.]+|[_.]+$/g, '');
    filename = filename.substring(0, 100) || 'scraped_content';

    return filename;
    // eslint-disable-next-line no-unused-vars
  } catch (_) {
    return 'scraped_content';
  }
}

/**
 * Save content to files
 * @param {string} markdown - Markdown content
 * @param {string} url - Original URL for filename generation
 * @returns {Promise<{markdownPath: string}>} Saved file paths
 */
async function saveFiles(markdown, url) {
  const baseFilename = createSafeFilename(url);
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

  const markdownFilename = `${baseFilename}_${timestamp}.md`;

  const markdownPath = path.resolve(markdownFilename);

  try {
    // Save markdown file
    await fs.writeFile(markdownPath, markdown, 'utf8');
    console.log(`📝 Markdown saved to: ${markdownFilename}`);

    return { markdownPath };
  } catch (error) {
    console.error('❌ Error saving files:', error.message);
    throw error;
  }
}

// CLI Help text
const HELP_TEXT = `
Page Extractor - A comprehensive web scraper built with Playwright

Usage:
  pnpm run cli <url> [options]

Arguments:
  url                   The URL to scrape (required)

Options:
  -q, --query <text>    Optional user query for focused content extraction
  -m, --mode <mode>     Scraping mode: normal or beast (default: normal)
  -p, --performance     Enable detailed performance monitoring (always enabled)
  -h, --help           Show this help message

Examples:
  pnpm run cli https://example.com
  pnpm run cli https://example.com -q "Find pricing information"
  pnpm run cli https://example.com -q "Extract product details" -m normal
  pnpm run cli https://example.com
  pnpm run cli https://example.com -q "Get contact information"

Description:
  This scraper extracts content from web pages, including:
  - Dynamic content revealed by user interactions
  - Lazy-loaded content via scrolling
  - Content from interactive elements like tabs, accordions, etc.

  The extracted content is automatically saved to timestamped files:
  - Markdown file (.md) - Clean, formatted content

  Performance monitoring is automatically enabled to track:
  - CPU and memory usage throughout the process
  - Browser launch and page load times
  - Network request counts
  - Phase-by-phase performance breakdown
  - Detailed reports with optimization recommendations

Browser Options:
  local          - Uses local Playwright browser installation (default)

Modes:
  normal - Simple extraction using page.content() only
  beast  - Advanced extraction with AI-powered interactive element detection

Environment Variables:
  GOOGLE_GEMINI_KEY - Required for AI-powered content extraction

The scraper uses AI to identify interactive elements and processes them
to reveal hidden content, then combines everything into a clean output.

The optional query parameter helps focus the AI on specific content types
or sections you're interested in extracting.
`;

// Parse command line arguments
function parseArgs(args) {
  const config = {
    url: null,
    query: '',
    mode: 'normal',
    showHelp: false,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === '-h' || arg === '--help') {
      config.showHelp = true;
    } else if (arg === '-q' || arg === '--query') {
      if (i + 1 < args.length) {
        config.query = args[i + 1];
        i++; // Skip next argument as it's the query text
      } else {
        console.error('Error: --query option requires a text value');
        process.exit(1);
      }
    } else if (arg === '-m' || arg === '--mode') {
      if (i + 1 < args.length) {
        const mode = args[i + 1].toLowerCase();
        if (mode === 'normal' || mode === 'beast') {
          config.mode = mode;
        } else {
          console.error('Error: --mode must be either "normal" or "beast"');
          process.exit(1);
        }
        i++; // Skip next argument as it's the mode value
      } else {
        console.error(
          'Error: --mode option requires a value (normal or beast)'
        );
        process.exit(1);
      }
    } else if (!config.url && !arg.startsWith('-')) {
      config.url = arg;
    }
  }

  return config;
}

// Validate URL
function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (error) {
    console.error('Error: Invalid URL provided', error);
    console.error(
      'Please provide a valid URL starting with http:// or https://'
    );
    return false;
  }
}

// Main CLI function
async function main() {
  // Validate GOOGLE_GEMINI_KEY
  if (!process.env.GOOGLE_GEMINI_KEY) {
    console.error('Error: GOOGLE_GEMINI_KEY environment variable is missing.');
    console.error('This key is required for AI-powered content extraction.');
    process.exit(1);
  }

  const args = process.argv.slice(2);
  const config = parseArgs(args);

  // Show help if requested or no arguments provided
  if (config.showHelp || args.length === 0) {
    console.log(HELP_TEXT);
    process.exit(0);
  }

  // Validate required arguments
  if (!config.url) {
    console.error('Error: URL is required');
    console.log('\nUse --help for usage information');
    process.exit(1);
  }

  if (!isValidUrl(config.url)) {
    console.error('Error: Invalid URL provided');
    console.error(
      'Please provide a valid URL starting with http:// or https://'
    );
    process.exit(1);
  }

  console.log('🚀 Starting Page Extractor...');
  console.log(`📄 URL: ${config.url}`);
  console.log(`🎯 Mode: ${config.mode}`);
  if (config.query) {
    console.log(`🔍 Query: ${config.query}`);
  }
  console.log('');

  // Convert mode string to constant
  const mode =
    config.mode === 'normal' ? SCRAPING_MODES.NORMAL : SCRAPING_MODES.BEAST;

  try {
    const result = await scrape(config.url, config.query, mode);

    if (result.success && result.data) {
      // Save files with extracted content
      await saveFiles(result.data.markdown, config.url);

      // Show any enhanced error warnings
      if (result.data.enhancedError) {
        console.log(`\n⚠️ Warning: ${result.data.enhancedError.userMessage}`);
        console.log(`🔍 Error ID: ${result.data.enhancedError.stackId}`);
      }
    } else {
      console.log('');
      console.error('❌ Scraping failed after all retry attempts');
      process.exit(1);
    }
  } catch (error) {
    console.log('');
    console.error('❌ Scraping failed with error:', error.message);
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', error => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the CLI
main().catch(error => {
  console.error('CLI Error:', error);
  process.exit(1);
});
