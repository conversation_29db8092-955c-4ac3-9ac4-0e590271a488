/**
 * Simple Logger - Clean, minimal logging system for essential information only
 * Shows only important progress updates while maintaining streaming functionality
 */
export class SimpleLogger {
  constructor(progressCallback = null) {
    this.progressCallback = progressCallback;
    this.isStreaming = progressCallback !== null;
    this.currentAction = null;
    this.startTime = Date.now();
    this.lastUpdate = 0;
    this.updateInterval = 2000; // Fixed 2-second intervals
    this.updateTimer = null;

    console.log('🚀 Starting scraping process...');
  }

  /**
   * Set the current action being performed
   * @param {string} action - Clear description of current action
   */
  setAction(action) {
    this.currentAction = action;
    this.lastUpdate = 0; // Force immediate update
    this._sendUpdate();
    this._startPeriodicUpdates();
  }

  /**
   * Update the current action (if different)
   * @param {string} action - New action description
   */
  updateAction(action) {
    if (this.currentAction !== action) {
      this.setAction(action);
    }
  }

  /**
   * Complete the current process
   * @param {string} message - Completion message
   * @param {Object} result - Optional result data
   */
  complete(message = 'Process completed successfully', result = {}) {
    this._stopPeriodicUpdates();

    const elapsed = Date.now() - this.startTime;
    const finalMessage = `✅ ${message}`;

    console.log(finalMessage);

    // Don't send 'completed' type messages from SimpleLogger
    // Only the API handler should send the final 'completed' message
    if (this.isStreaming) {
      this._streamData({
        type: 'info',
        message: finalMessage,
        elapsed,
        result,
      });
    }
  }

  /**
   * Log an error
   * @param {Error|string} error - Error to log
   * @param {string} context - Additional context
   */
  error(error, context = '') {
    this._stopPeriodicUpdates();

    const errorMessage = error instanceof Error ? error.message : error;
    const fullMessage = context ? `${context}: ${errorMessage}` : errorMessage;

    console.error(`❌ ${fullMessage}`);

    if (this.isStreaming) {
      this._streamData({
        type: 'error',
        message: fullMessage,
        error: errorMessage,
      });
    }
  }

  /**
   * Log a simple message
   * @param {string} message - Message to log
   * @param {string} level - Log level (info, warn, error)
   */
  log(message, level = 'info') {
    const emoji = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';

    console.log(`${emoji} ${message}`);

    if (this.isStreaming) {
      this._streamData({
        type: 'log',
        level,
        message,
      });
    }
  }

  /**
   * Send periodic updates every 2 seconds
   * @private
   */
  _sendUpdate() {
    if (!this.currentAction) return;

    const now = Date.now();
    const elapsed = now - this.startTime;

    // Only send if 2 seconds have passed or it's the first update
    if (now - this.lastUpdate >= this.updateInterval || this.lastUpdate === 0) {
      const message = `⚡ ${this.currentAction}`;

      console.log(message);

      if (this.isStreaming) {
        this._streamData({
          type: 'progress',
          action: this.currentAction,
          message,
          elapsed,
        });
      }

      this.lastUpdate = now;
    }
  }

  /**
   * Start periodic updates every 2 seconds
   * @private
   */
  _startPeriodicUpdates() {
    this._stopPeriodicUpdates();

    this.updateTimer = setInterval(() => {
      this._sendUpdate();
    }, this.updateInterval);
  }

  /**
   * Stop periodic updates
   * @private
   */
  _stopPeriodicUpdates() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }
  }

  /**
   * Stream data to API if streaming is enabled
   * @param {Object} data - Data to stream
   * @private
   */
  _streamData(data) {
    if (!this.isStreaming || !this.progressCallback) return;

    try {
      const streamData = {
        timestamp: new Date().toISOString(),
        ...data,
      };

      // Handle both sync and async callbacks
      const result = this.progressCallback(streamData);
      if (result && typeof result.then === 'function') {
        result.catch(err => console.error('Streaming error:', err));
      }
    } catch (error) {
      console.error('Error streaming data:', error);
    }
  }

  /**
   * Cleanup resources
   */
  destroy() {
    this._stopPeriodicUpdates();
  }
}

/**
 * Create a new SimpleLogger instance
 * @param {Function} progressCallback - Optional callback for streaming progress
 * @returns {SimpleLogger} New logger instance
 */
export function createSimpleLogger(progressCallback = null) {
  return new SimpleLogger(progressCallback);
}
