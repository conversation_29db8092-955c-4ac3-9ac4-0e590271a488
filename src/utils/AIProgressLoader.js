/**
 * AIProgressLoader - Handles progress indicators and spinners for AI operations
 * Provides animated progress feedback without streaming actual AI text content
 */

export class AIProgressLoader {
  constructor(streamingService = null) {
    this.streamingService = streamingService;
    this.currentOperation = null;
    this.intervalId = null;
    this.startTime = null;
    this.frameIndex = 0;
    this.lastProgressTime = 0;

    // Different spinner styles for different operations
    this.spinners = {
      dots: ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'],
      dots2: ['⣾', '⣽', '⣻', '⢿', '⡿', '⣟', '⣯', '⣷'],
      robot: ['🤖', '🔧', '⚙️', '🧠', '💭', '✨', '🔮', '🤖'],
      thinking: ['🤔', '💭', '🧠', '⚡', '💡', '✨', '🔍', '📝'],
      processing: ['⚡', '⚙️', '🔧', '🛠️', '⚡', '✨', '🔮', '📊'],
    };

    // Progress messages for different stages
    this.progressMessages = {
      initializing: [
        'Initializing AI model...',
        'Preparing AI context...',
        'Setting up AI parameters...',
      ],
      analyzing: [
        'AI is analyzing content...',
        'Processing with AI intelligence...',
        'Examining structure and patterns...',
        'Identifying key elements...',
        'Understanding content context...',
      ],
      converting: [
        'AI is converting content...',
        'Transforming structure...',
        'Optimizing markdown format...',
        'Enhancing readability...',
        'Finalizing conversion...',
      ],
      completing: [
        'Finishing AI processing...',
        'Preparing final results...',
        'Optimizing output...',
      ],
    };
  }

  /**
   * Start an AI operation with progress indication
   * @param {string} operationType - Type of operation (analyzing, converting, etc.)
   * @param {string} customMessage - Custom message to display
   * @param {string} spinnerType - Type of spinner to use
   */
  startOperation(
    operationType = 'processing',
    customMessage = null,
    spinnerType = 'robot'
  ) {
    this.stopOperation(); // Stop any existing operation

    this.currentOperation = {
      type: operationType,
      customMessage,
      spinnerType,
      messageIndex: 0,
      phaseStartTime: Date.now(),
    };

    this.startTime = Date.now();
    this.frameIndex = 0;
    this.lastProgressTime = 0;

    // Start the animation
    this.startAnimation();

    // Log initial message
    const initialMessage =
      customMessage || this.getProgressMessage(operationType, 0);
    this.log(`🚀 ${initialMessage}`);

    // Stream progress if streaming service is available
    if (this.streamingService) {
      this.streamingService.updateProgress({
        message: initialMessage,
        aiOperation: {
          type: operationType,
          status: 'started',
          spinner: this.getCurrentSpinner(),
          elapsed: 0,
        },
      });
    }
  }

  /**
   * Update the progress with a new message or stage
   * @param {string} message - Progress message
   * @param {Object} additionalData - Additional data to include
   */
  updateProgress(message = null, additionalData = {}) {
    if (!this.currentOperation) return;

    const elapsed = Date.now() - this.startTime;
    const progressMessage = message || this.getNextProgressMessage();

    // Don't spam updates - minimum 500ms between messages for real-time streaming
    if (elapsed - this.lastProgressTime < 500 && !message) {
      return;
    }

    this.lastProgressTime = elapsed;
    this.log(`${this.getCurrentSpinner()} ${progressMessage}`);

    // Stream progress if streaming service is available
    if (this.streamingService) {
      this.streamingService.updateProgress({
        message: progressMessage,
        aiOperation: {
          type: this.currentOperation.type,
          status: 'progress',
          spinner: this.getCurrentSpinner(),
          elapsed,
          ...additionalData,
        },
      });
    }
  }

  /**
   * Complete the current AI operation
   * @param {string} completionMessage - Final completion message
   * @param {Object} result - Operation result data
   */
  completeOperation(completionMessage = null, result = {}) {
    if (!this.currentOperation) return;

    const elapsed = Date.now() - this.startTime;
    const finalMessage =
      completionMessage ||
      `✅ AI ${this.currentOperation.type} completed successfully!`;

    this.stopAnimation();
    this.log(finalMessage);

    // Stream completion if streaming service is available
    if (this.streamingService) {
      this.streamingService.updateProgress({
        message: finalMessage,
        aiOperation: {
          type: this.currentOperation.type,
          status: 'completed',
          elapsed,
          result,
        },
      });
    }

    this.currentOperation = null;
  }

  /**
   * Handle AI operation error
   * @param {Error} error - Error that occurred
   * @param {string} errorMessage - Custom error message
   */
  errorOperation(error, errorMessage = null) {
    if (!this.currentOperation) return;

    const elapsed = Date.now() - this.startTime;
    const finalMessage =
      errorMessage ||
      `❌ AI ${this.currentOperation.type} failed: ${error.message}`;

    this.stopAnimation();
    this.log(finalMessage, 'error');

    // Stream error if streaming service is available
    if (this.streamingService) {
      this.streamingService.updateProgress({
        message: finalMessage,
        aiOperation: {
          type: this.currentOperation.type,
          status: 'error',
          elapsed,
          error: {
            message: error.message,
            code: error.code || 'AI_OPERATION_ERROR',
          },
        },
      });
    }

    this.currentOperation = null;
  }

  /**
   * Stop the current operation
   */
  stopOperation() {
    this.stopAnimation();
    this.currentOperation = null;
  }

  /**
   * Start the spinner animation
   */
  startAnimation() {
    if (this.intervalId) return;

    this.intervalId = setInterval(() => {
      this.frameIndex++;

      // Update spinner in console (overwrite current line)
      if (this.currentOperation) {
        const spinner = this.getCurrentSpinner();
        const message =
          this.currentOperation.customMessage ||
          this.getProgressMessage(
            this.currentOperation.type,
            this.currentOperation.messageIndex
          );

        // Clear current line and write new spinner state
        process.stdout.write(`\r${spinner} ${message}...`);
      }
    }, 150); // Update every 150ms for smooth animation
  }

  /**
   * Stop the spinner animation
   */
  stopAnimation() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;

      // Clear the spinner line
      process.stdout.write(`\r${' '.repeat(80)}\r`);
    }
  }

  /**
   * Get the current spinner frame
   * @returns {string} Current spinner character/emoji
   */
  getCurrentSpinner() {
    if (!this.currentOperation) return '⚡';

    const spinner =
      this.spinners[this.currentOperation.spinnerType] || this.spinners.robot;
    return spinner[this.frameIndex % spinner.length];
  }

  /**
   * Get a progress message for the current stage
   * @param {string} operationType - Type of operation
   * @param {number} index - Message index
   * @returns {string} Progress message
   */
  getProgressMessage(operationType, index = 0) {
    const messages =
      this.progressMessages[operationType] || this.progressMessages.processing;
    return messages[index % messages.length];
  }

  /**
   * Get the next progress message in sequence
   * @returns {string} Next progress message
   */
  getNextProgressMessage() {
    if (!this.currentOperation) return 'Processing...';

    this.currentOperation.messageIndex++;
    return this.getProgressMessage(
      this.currentOperation.type,
      this.currentOperation.messageIndex
    );
  }

  /**
   * Log a message (with optional level)
   * @param {string} message - Message to log
   * @param {string} level - Log level
   */
  log(message, level = 'info') {
    if (this.streamingService) {
      this.streamingService.log(message, level);
    } else {
      console.log(`[${level.toUpperCase()}] ${message}`);
    }
  }

  /**
   * Create a progress callback for AI streaming operations
   * @param {string} operationType - Type of AI operation
   * @param {string} spinnerType - Type of spinner to use
   * @returns {Function} Progress callback function
   */
  createProgressCallback(operationType = 'processing', spinnerType = 'robot') {
    let chunkCount = 0;
    let lastUpdate = 0;

    return {
      onStart: (customMessage = null) => {
        this.startOperation(operationType, customMessage, spinnerType);
      },

      onProgress: () => {
        chunkCount++;
        const now = Date.now();

        // Update progress every 1 second or every 25 chunks for more responsive streaming
        if (now - lastUpdate > 1000 || chunkCount % 25 === 0) {
          this.updateProgress(null, {
            chunksProcessed: chunkCount,
            processingRate: `${chunkCount} chunks processed`,
          });
          lastUpdate = now;
        }
      },

      onComplete: (result = {}) => {
        this.completeOperation(null, {
          chunksProcessed: chunkCount,
          ...result,
        });
      },

      onError: error => {
        this.errorOperation(error);
      },
    };
  }

  /**
   * Get operation statistics
   * @returns {Object} Current operation stats
   */
  getStats() {
    if (!this.currentOperation) {
      return { active: false };
    }

    return {
      active: true,
      type: this.currentOperation.type,
      elapsed: Date.now() - this.startTime,
      currentSpinner: this.getCurrentSpinner(),
      messageIndex: this.currentOperation.messageIndex,
    };
  }
}

/**
 * Create a new AI Progress Loader instance
 * @param {Object} streamingService - Optional streaming service instance
 * @returns {AIProgressLoader} New loader instance
 */
export function createAIProgressLoader(streamingService = null) {
  return new AIProgressLoader(streamingService);
}

/**
 * Global AI progress loader for simple usage
 */
export const globalAILoader = new AIProgressLoader();
