import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { z } from 'zod';
import { scrapeWithStreaming } from './WebScraper.js';
import { serve } from '@hono/node-server';
import { getKey } from './utils/KeyManager.js';

const ScrapeRequestSchema = z.object({
  url: z.string().url('Invalid URL format'),
  query: z.string().optional().default(''),
  mode: z.enum(['normal', 'beast']).optional().default('normal'),
});

const app = new Hono();

app.use(
  '*',
  cors({
    origin: ['*'],
    allowHeaders: ['Content-Type'],
    allowMethods: ['GET', 'POST', 'OPTIONS'],
  })
);

app.use('*', logger());

app.get('/', c => {
  console.log('📋 Health check requested');
  return c.json({
    status: 'healthy',
    service: 'page-extractor-playwright',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  });
});

app.get('/health', c => {
  console.log('🔍 Detailed health check requested');
  const memoryUsage = process.memoryUsage();
  const uptime = process.uptime();
  console.log(
    `💾 Memory usage: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB / ${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`
  );
  console.log(`⏱️  Uptime: ${Math.round(uptime)}s`);

  return c.json({
    status: 'healthy',
    checks: {
      memory: memoryUsage,
      uptime: uptime,
      timestamp: new Date().toISOString(),
      environment: {
        googleApiKeyExists: !!getKey(),
        googleApiKeyLength: getKey()?.length || 0,
        googleApiKeyPrefix:
          getKey()?.substring(0, 10) || 'undefined',
      },
    },
  });
});

app.post('/scrape', async c => {
  try {
    const body = await c.req.json();
    const validatedData = ScrapeRequestSchema.parse(body);

    // Validate GOOGLE_GEMINI_KEY
    if (!getKey()) {
      console.error('GOOGLE_GEMINI_KEY is missing from environment variables.');
      return c.json(
        {
          success: false,
          error: {
            message: 'Internal Server Error',
            timestamp: new Date().toISOString(),
          },
        },
        500
      );
    }

    const { url, query, mode } = validatedData;

    console.log('🌐 Starting streaming scrape request:');
    console.log(`   📍 URL: ${url}`);
    console.log(`   🔍 Query: ${query || 'None'}`);
    console.log(`   ⚡ Mode: ${mode}`);
    console.log(
      `   📡 Client IP: ${c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'Unknown'}`
    );

    // Create a TransformStream for real-time SSE
    let sseController;
    let isStreamClosed = false;
    const sseStream = new ReadableStream({
      start(controller) {
        sseController = controller;
      },
    });

    const writeSSE = async data => {
      if (isStreamClosed || !sseController) {
        return; // Don't try to write to a closed stream
      }

      const sseData = `data: ${JSON.stringify(data)}\n\n`;

      try {
        // Check if controller is still valid before writing
        if (sseController.desiredSize !== null) {
          // Encode and enqueue immediately for real-time streaming
          sseController.enqueue(new TextEncoder().encode(sseData));

          // Force a micro-delay to help with buffering issues
          await new Promise(resolve => setTimeout(resolve, 1));
        } else {
          isStreamClosed = true;
        }
      } catch (error) {
        // Only log errors that aren't about the controller being closed
        if (
          !error.message.includes('Controller is already closed') &&
          !error.message.includes('Invalid state')
        ) {
          console.error('Error writing SSE data:', error);
        }
        isStreamClosed = true; // Mark as closed if we encounter an error
      }
    };

    const closeSSE = () => {
      if (!isStreamClosed && sseController) {
        try {
          // Check if the controller is still in a valid state before closing
          if (sseController.desiredSize !== null) {
            sseController.close();
          }
          isStreamClosed = true;
        } catch (error) {
          // Only log errors that aren't about the controller already being closed
          if (
            !error.message.includes('Controller is already closed') &&
            !error.message.includes('Invalid state')
          ) {
            console.error('Error closing SSE stream:', error);
          }
          isStreamClosed = true;
        }
      }
    };

    // Add a timeout to prevent hanging streams
    const timeoutId = setTimeout(() => {
      if (!isStreamClosed) {
        console.log('Stream timeout reached, closing connection');
        writeSSE({
          type: 'error',
          timestamp: new Date().toISOString(),
          error: {
            message: 'Request timeout',
            details: 'The scraping operation took too long to complete',
          },
        }).finally(() => {
          if (!isStreamClosed) {
            closeSSE();
          }
        });
      }
    }, 300000); // 5 minutes timeout

    // Start the scraping process asynchronously
    (async () => {
      try {
        await writeSSE({
          type: 'connected',
          timestamp: new Date().toISOString(),
          message: 'Stream connected, starting scraping process...',
        });

        let scrapingResult = null;
        let hasError = false;

        try {
          const progressCallback = async progress => {
            await writeSSE({
              type: 'progress',
              timestamp: new Date().toISOString(),
              ...progress,
            });
          };

          scrapingResult = await scrapeWithStreaming(
            url,
            query,
            mode,
            progressCallback
          );
        } catch (error) {
          hasError = true;
          console.error('Scraping error:', error);

          await writeSSE({
            type: 'error',
            timestamp: new Date().toISOString(),
            error: {
              message: error.message,
              code: error.code || 'SCRAPING_FAILED',
              details: error.details || 'An error occurred during scraping',
            },
          });
        }

        if (!hasError && scrapingResult) {
          await writeSSE({
            type: 'completed',
            timestamp: new Date().toISOString(),
            result: {
              success: scrapingResult.success,
              data: {
                markdown: scrapingResult.markdown,
                metadata: {
                  url: url,
                  query: query,
                  mode: mode,
                  processingTime: scrapingResult.processingTime,
                  contentLength: {
                    markdown: scrapingResult.markdown?.length || 0,
                  },
                },
              },
              enhancedError: scrapingResult.enhancedError || null,
            },
          });
        } else if (!hasError) {
          await writeSSE({
            type: 'completed',
            timestamp: new Date().toISOString(),
            result: {
              success: false,
              error: 'Scraping completed but no data was extracted',
            },
          });
        }

        await writeSSE({
          type: 'done',
          timestamp: new Date().toISOString(),
        });

        // Clear timeout and close the stream
        clearTimeout(timeoutId);
        if (!isStreamClosed) {
          closeSSE();
        }
      } catch (error) {
        console.error('SSE streaming error:', error);
        if (!isStreamClosed) {
          await writeSSE({
            type: 'error',
            timestamp: new Date().toISOString(),
            error: {
              message: 'Internal streaming error',
              details: error.message,
            },
          });
        }
        clearTimeout(timeoutId);
        if (!isStreamClosed) {
          closeSSE();
        }
      }
    })();

    // Return the SSE response with proper headers
    return new Response(sseStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control, Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'X-Accel-Buffering': 'no',
        'Transfer-Encoding': 'chunked',
      },
    });
  } catch (error) {
    console.error('Request validation error:', error);

    return c.json(
      {
        success: false,
        error: {
          message: 'Invalid request data',
          details: error.errors || error.message,
          timestamp: new Date().toISOString(),
        },
      },
      400
    );
  }
});

app.post('/scrape-sync', async c => {
  try {
    const body = await c.req.json();
    const validatedData = ScrapeRequestSchema.parse(body);

    const { url, query, mode } = validatedData;

    console.log('🔄 Starting synchronous scrape request:');
    console.log(`   📍 URL: ${url}`);
    console.log(`   🔍 Query: ${query || 'None'}`);
    console.log(`   ⚡ Mode: ${mode}`);
    console.log(
      `   📡 Client IP: ${c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'Unknown'}`
    );

    // Execute scraping without streaming
    const startTime = Date.now();
    const result = await scrapeWithStreaming(url, query, mode, null);
    const processingTime = Date.now() - startTime;

    console.log(`✅ Sync scrape completed in ${processingTime}ms`);
    console.log(`   📄 Markdown length: ${result.markdown?.length || 0} chars`);
    console.log(`   🌐 HTML length: ${result.html?.length || 0} chars`);

    return c.json({
      success: result.success,
      data: {
        markdown: result.markdown,
        metadata: {
          url: url,
          query: query,
          mode: mode,
          processingTime: result.processingTime,
          contentLength: {
            markdown: result.markdown?.length || 0,
          },
        },
      },
      enhancedError: result.enhancedError || null,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Sync scraping error:', error);

    return c.json(
      {
        success: false,
        error: {
          message: error.message,
          code: error.code || 'SCRAPING_FAILED',
          timestamp: new Date().toISOString(),
        },
      },
      500
    );
  }
});

app.onError((err, c) => {
  console.error('Application error:', err);

  return c.json(
    {
      success: false,
      error: {
        message: 'Internal server error',
        timestamp: new Date().toISOString(),
      },
    },
    500
  );
});

app.notFound(c => {
  return c.json(
    {
      success: false,
      error: {
        message: 'Endpoint not found',
        availableEndpoints: [
          'GET /',
          'GET /health',
          'POST /scrape',
          'POST /scrape-sync',
        ],
        timestamp: new Date().toISOString(),
      },
    },
    404
  );
});

const port = process.env.PORT || 3000;
console.log('🚀 Starting Page Extractor Playwright Server...');
console.log(`📡 Server will be available at: http://localhost:${port}`);
console.log('📋 Available endpoints:');
console.log('   GET  /         - Health check');
console.log('   GET  /health   - Detailed health check');
console.log('   POST /scrape   - Stream scraping (Server-Sent Events)');
console.log('   POST /scrape-sync - Synchronous scraping');
console.log('');
console.log('📖 Usage examples:');
console.log('');
console.log(
  '------------------------------------------------------------------------------------------------'
);
console.log('');

console.log(`   curl -X POST http://localhost:${port}/scrape \\`);
console.log('        -H "Content-Type: application/json" \\');
console.log('        -d \'{"url": "https://example.com", "mode": "normal"}\'');
console.log('');
console.log(
  '------------------------------------------------------------------------------------------------'
);
console.log('');

serve({
  fetch: app.fetch,
  port: port,
  hostname: '0.0.0.0',
});

console.log(`✅ Server started successfully on port ${port}!`);
console.log('🎯 Ready to process scraping requests...');

export { app };
